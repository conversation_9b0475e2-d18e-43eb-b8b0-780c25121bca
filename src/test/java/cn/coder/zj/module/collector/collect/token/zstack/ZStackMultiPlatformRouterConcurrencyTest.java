package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ZStack多平台路由器并发安全测试
 * 
 * 验证修复后的ZStackMultiPlatformRouter在高并发场景下的线程安全性
 */
@Slf4j
public class ZStackMultiPlatformRouterConcurrencyTest {

    private List<Platform> testPlatforms;
    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        // 清理之前的状态
        ZStackMultiPlatformRouter.clearAllPlatformConfigs();
        
        // 创建测试平台
        testPlatforms = createTestPlatforms();
        
        // 创建线程池
        executorService = Executors.newFixedThreadPool(20);
    }

    @Test
    @DisplayName("多平台并发访问线程安全测试")
    void testConcurrentMultiPlatformAccess() throws InterruptedException {
        int threadCount = 15;
        int operationsPerThread = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        // 启动多个线程并发访问不同平台
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executorService.submit(() -> {
                try {
                    startLatch.await(); // 等待统一开始信号
                    
                    for (int j = 0; j < operationsPerThread; j++) {
                        Platform platform = testPlatforms.get(threadIndex % testPlatforms.size());
                        
                        try {
                            // 模拟ZStack API调用
                            String result = ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                                // 模拟API调用耗时
                                try {
                                    Thread.sleep(10 + (int)(Math.random() * 20));
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }
                                return "Success-" + platform.getPlatformName() + "-" + Thread.currentThread().getName();
                            });
                            
                            successCount.incrementAndGet();
                            log.debug("线程 {} 平台 {} 操作成功: {}", 
                                Thread.currentThread().getName(), platform.getPlatformName(), result);
                                
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            log.error("线程 {} 平台 {} 操作失败: {}", 
                                Thread.currentThread().getName(), platform.getPlatformName(), e.getMessage());
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    finishLatch.countDown();
                }
            });
        }
        
        // 开始并发测试
        log.info("开始并发测试: {} 个线程，每线程 {} 次操作", threadCount, operationsPerThread);
        long startTime = System.currentTimeMillis();
        startLatch.countDown();
        
        // 等待所有线程完成
        boolean finished = finishLatch.await(60, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        
        // 输出测试结果
        log.info("=== 并发测试结果 ===");
        log.info("测试完成: {}", finished ? "是" : "超时");
        log.info("总耗时: {} ms", endTime - startTime);
        log.info("成功操作数: {}", successCount.get());
        log.info("失败操作数: {}", errorCount.get());
        log.info("总操作数: {}", threadCount * operationsPerThread);
        log.info("成功率: {:.2f}%", (double) successCount.get() / (threadCount * operationsPerThread) * 100);
        
        // 输出路由器统计信息
        log.info("\n{}", ZStackMultiPlatformRouter.getDetailedRouterStats());
        
        // 验证结果
        assert finished : "测试未在预期时间内完成";
        assert errorCount.get() == 0 : "存在操作失败，错误数: " + errorCount.get();
        assert successCount.get() == threadCount * operationsPerThread : "成功操作数不符合预期";
    }

    @Test
    @DisplayName("配置切换性能测试")
    void testConfigurationSwitchingPerformance() throws InterruptedException {
        int switchCount = 100;
        CountDownLatch latch = new CountDownLatch(switchCount);
        AtomicInteger actualSwitches = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 快速切换平台配置
        for (int i = 0; i < switchCount; i++) {
            final int index = i;
            executorService.submit(() -> {
                try {
                    Platform platform = testPlatforms.get(index % testPlatforms.size());
                    ZStackMultiPlatformRouter.executeWithPlatform(platform, () -> {
                        actualSwitches.incrementAndGet();
                        return "OK";
                    });
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        
        log.info("=== 配置切换性能测试结果 ===");
        log.info("总耗时: {} ms", endTime - startTime);
        log.info("平均每次切换耗时: {:.2f} ms", (double)(endTime - startTime) / switchCount);
        log.info("实际执行次数: {}", actualSwitches.get());
        log.info("\n{}", ZStackMultiPlatformRouter.getRouterStats());
    }

    /**
     * 创建测试用的平台对象
     */
    private List<Platform> createTestPlatforms() {
        List<Platform> platforms = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            Platform platform = new Platform();
            platform.setPlatformId((long) i);
            platform.setPlatformName("测试平台-" + i);
            platform.setPlatformUrl("http://192.168.1." + (100 + i) + ":8080");
            platform.setUsername("admin");
            platform.setPassword("password");
            platform.setAkType(0); // sessionId模式
            platforms.add(platform);
        }
        
        return platforms;
    }
}
