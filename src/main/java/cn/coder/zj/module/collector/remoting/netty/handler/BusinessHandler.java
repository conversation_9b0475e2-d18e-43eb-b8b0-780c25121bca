package cn.coder.zj.module.collector.remoting.netty.handler;

import cn.coder.zj.module.collector.collect.strategy.TokenStrategyFactory;
import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.job.cache.TaskCacheModel;
import cn.coder.zj.module.collector.job.service.ScheduleTaskService;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;

import static cn.coder.zj.module.collector.job.annotation.YQJobProcessor.TASK_CACHE;

/**
 * 业务数据推送处理器
 * 优化：增强消息处理的准确性和可靠性
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class BusinessHandler extends SimpleChannelInboundHandler<ClusterMsg.Message> {

    private final TaskExecutor taskExecutor;

    private final CollectorConfig collectorConfig;

    private final ScheduleTaskService scheduleTaskService;

    private final AutoDiscoveryService autoDiscoveryService;

    public BusinessHandler(TaskExecutor taskExecutor, ScheduleTaskService scheduleTaskService, CollectorConfig collectorConfig, AutoDiscoveryService autoDiscoveryService) {
        this.taskExecutor = taskExecutor;
        this.scheduleTaskService = scheduleTaskService;
        this.collectorConfig = collectorConfig;
        this.autoDiscoveryService = autoDiscoveryService;
    }

    /**
     * 读取到消息后进行处理
     * 优化：增加消息类型日志和客户端ID验证
     *
     * @param ctx         channel上下文
     * @param messageInfo 发送消息
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        try {
            String jsonStr = messageInfo.getData();
            String messageClientId = messageInfo.getClientId();
            String localClientId = collectorConfig.getClientId();

            // 验证消息是否属于当前客户端
            if (!StringUtils.hasText(messageClientId) || !messageClientId.equals(localClientId)) {
                log.warn("[消息验证失败] 收到不属于本客户端的消息 - 消息客户端ID: {}, 本地客户端ID: {}, 消息类型: {}",
                        messageClientId, localClientId, messageInfo.getType());
                return;
            }

            log.debug("[消息处理] 客户端: {}, 消息类型: {}, 数据长度: {}",
                    messageClientId, messageInfo.getType(), jsonStr != null ? jsonStr.length() : 0);

            switch (messageInfo.getType().getNumber()) {
                case ClusterMsg.MessageType.INFO_VALUE, ClusterMsg.MessageType.UPDATE_VALUE: {
                    if (ClusterMsg.MessageType.UPDATE_VALUE == messageInfo.getType().getNumber()) {
                        log.info("[平台信息更新] 客户端: {}, 收到更新数据指令", messageClientId);
                    }
                    processPlatformInfo(jsonStr, messageClientId);
                    break;
                }
                case ClusterMsg.MessageType.SCHEDULED_START_VALUE: {
                    log.info("[定时任务启动] 客户端: {}, 开始处理定时任务", messageClientId);
                    scheduledStart(jsonStr);
                    break;
                }
                case ClusterMsg.MessageType.SCHEDULED_INFO_VALUE: {
                    log.debug("[定时任务信息] 客户端: {}, 获取定时任务信息", messageClientId);
                    scheduledInfo();
                    break;
                }
                case ClusterMsg.MessageType.ONLINE_VALUE: {
                    log.info("[客户端上线] 客户端: {}, 处理上线消息", messageClientId);
                    processOnline();
                    break;
                }
                case ClusterMsg.MessageType.OFFLINE_VALUE: {
                    log.info("[客户端下线] 客户端: {}, 处理下线消息", messageClientId);
                    processOffline();
                    break;
                }
                case ClusterMsg.MessageType.OTHER_VALUE: {
                    log.debug("[其他消息] 客户端: {}, 指标: {}", messageClientId, messageInfo.getMetrics());
                    autoDiscoveryService.autoDiscovery(jsonStr, messageInfo.getMetrics());
                    break;
                }
                default: {
                    log.warn("[未知消息类型] 客户端: {}, 消息类型: {}", messageClientId, messageInfo.getType());
                }
            }
        } catch (Exception e) {
            log.error("[消息处理异常] 客户端: {}, 消息类型: {}, 异常: {}",
                    messageInfo.getClientId(), messageInfo.getType(), e.getMessage(), e);
        } finally {
            ReferenceCountUtil.release(messageInfo);
        }
    }

    /**
     * 任务信息
     */
    private void scheduledInfo() {
        try {
            SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
            List<TaskCacheModel> cacheModels = scheduleTaskService.listTasks();

            ClusterMsg.Message response = ClusterMsg.Message.newBuilder()
                    .setData(new GsonBuilder()
                            .excludeFieldsWithoutExposeAnnotation()
                            .create().toJson(cacheModels))
                    .setClientId(collectorConfig.getClientId())
                    .setType(ClusterMsg.MessageType.SCHEDULED_INFO)
                    .setTime(System.currentTimeMillis())
                    .build();

            sendMessageService.sendMessage(CacheService.getCtx("ctx"), response);
            log.info("[定时任务信息] 客户端: {}, 发送任务信息成功, 任务数量: {}",
                    collectorConfig.getClientId(), cacheModels.size());

        } catch (Exception e) {
            log.error("[定时任务信息] 客户端: {}, 发送任务信息失败: {}",
                    collectorConfig.getClientId(), e.getMessage(), e);
        }
    }

    /**
     * 处理任务
     */
    private void scheduledStart(String jsonStr) {
        try {
            scheduleTaskService.clearAllTasks();
            Gson gson = new Gson();
            Type listType = new TypeToken<List<String>>() {}.getType();
            List<String> jobNames = gson.fromJson(jsonStr, listType);

            int successCount = 0;
            for (String jobName : jobNames) {
                TaskCacheModel taskCacheModel = TASK_CACHE.get(jobName);
                if (taskCacheModel != null) {
                    taskCacheModel.setStatus(1);
                    scheduleTaskService.addTask(taskCacheModel);
                    successCount++;
                } else {
                    log.warn("[定时任务] 未找到任务: {}", jobName);
                }
            }

            log.info("[定时任务启动] 客户端: {}, 启动任务成功: {}/{}",
                    collectorConfig.getClientId(), successCount, jobNames.size());

        } catch (Exception e) {
            log.error("[定时任务启动] 客户端: {}, 启动任务失败: {}",
                    collectorConfig.getClientId(), e.getMessage(), e);
        }
    }

    /**
     * 处理平台信息
     * 优化：增加客户端ID验证和详细日志
     */
    private void processPlatformInfo(String jsonStr, String clientId) {
        try {
            Type listType = new TypeToken<List<Platform>>() {}.getType();
            List<Platform> platformInfo = new Gson().fromJson(jsonStr, listType);

            log.info("[平台信息处理] 客户端: {}, 收到平台数量: {}", clientId, platformInfo.size());

            for (Platform p : platformInfo) {
                // 验证平台信息的完整性
                if (!validatePlatform(p)) {
                    log.warn("[平台信息验证] 客户端: {}, 平台信息不完整，跳过处理: {}", clientId, p.getPlatformName());
                    continue;
                }

                taskExecutor.execute(() -> {
                    try {
                        log.info("[平台处理开始] 客户端: {}, 平台: {} (ID: {}, Type: {})",
                                clientId, p.getPlatformName(), p.getPlatformId(), p.getTypeCode());

                        AbstractToken abstractCollect = TokenStrategyFactory.invoke(p.getTypeCode());

                        // 先执行preCheck进行连接验证（不更新状态）
                        abstractCollect.preCheck(p);

                        // 再执行token方法获取认证信息并更新最终状态
                        abstractCollect.token(p);

                        log.info("[平台处理完成] 客户端: {}, 平台: {} 处理成功", clientId, p.getPlatformName());

                    } catch (Exception e) {
                        log.error("[平台处理异常] 客户端: {}, 平台: {} 处理失败: {}",
                                clientId, p.getPlatformName(), e.getMessage(), e);

                        // 确保异常情况下也能更新平台状态
                        try {
                            AbstractToken abstractCollect = TokenStrategyFactory.invoke(p.getTypeCode());
                            // 使用反射调用updatePlatformStatus方法（如果存在）
                            java.lang.reflect.Method updateMethod = abstractCollect.getClass()
                                    .getDeclaredMethod("updatePlatformStatus", Platform.class, boolean.class);
                            updateMethod.setAccessible(true);
                            updateMethod.invoke(abstractCollect, p, false);

                            log.info("[平台状态更新] 客户端: {}, 平台: {} 异常状态更新成功", clientId, p.getPlatformName());

                        } catch (Exception ex) {
                            log.error("[平台状态更新失败] 客户端: {}, 平台: {} 异常状态更新失败: {}",
                                    clientId, p.getPlatformName(), ex.getMessage());
                        }
                    }
                });
            }

        } catch (Exception e) {
            log.error("[平台信息处理] 客户端: {}, 处理平台信息失败: {}", clientId, e.getMessage(), e);
        }
    }

    /**
     * 验证平台信息的完整性
     */
    private boolean validatePlatform(Platform platform) {
        if (platform == null) {
            return false;
        }

        if (!StringUtils.hasText(platform.getPlatformName())) {
            log.warn("[平台验证] 平台名称为空");
            return false;
        }

        if (!StringUtils.hasText(platform.getTypeCode())) {
            log.warn("[平台验证] 平台类型为空: {}", platform.getPlatformName());
            return false;
        }

        if (platform.getPlatformId() == null) {
            log.warn("[平台验证] 平台ID为空: {}", platform.getPlatformName());
            return false;
        }

        return true;
    }

    /**
     * 处理上线消息
     */
    private void processOnline() {
        try {
            Collection<TaskCacheModel> values = TASK_CACHE.values();
            int count = 0;

            for (TaskCacheModel taskCacheModel : values) {
                taskCacheModel.setStatus(1);
                scheduleTaskService.addTask(taskCacheModel);
                count++;
            }

            log.info("[客户端上线] 客户端: {}, 启动任务数量: {}", collectorConfig.getClientId(), count);

        } catch (Exception e) {
            log.error("[客户端上线] 客户端: {}, 处理上线消息失败: {}",
                    collectorConfig.getClientId(), e.getMessage(), e);
        }
    }

    /**
     * 处理下线消息
     */
    private void processOffline() {
        try {
            Collection<TaskCacheModel> values = TASK_CACHE.values();
            int count = 0;

            for (TaskCacheModel taskCacheModel : values) {
                taskCacheModel.setStatus(0);
                scheduleTaskService.removeTask(taskCacheModel);
                count++;
            }

            log.info("[客户端下线] 客户端: {}, 停止任务数量: {}", collectorConfig.getClientId(), count);

        } catch (Exception e) {
            log.error("[客户端下线] 客户端: {}, 处理下线消息失败: {}",
                    collectorConfig.getClientId(), e.getMessage(), e);
        }
    }
}
