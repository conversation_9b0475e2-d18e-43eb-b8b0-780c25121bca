package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.CommonUtil;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_VM;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;


    private static final BigDecimal ZERO = new BigDecimal(0);
    private static final String KVM = "KVM";
    private static final String VM_UUID_PREFIX = "VMUuid=";


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
            List<VmData> vmDataList = new ArrayList<>();
            String token = platform.getZsTackPlatform().getToken();
            // 查询区域列表
            QueryZoneAction zoneAction = new QueryZoneAction();
            if (platform.getAkType() == 0) {
                zoneAction.sessionId = token;
            } else {
                zoneAction.accessKeyId = platform.getUsername();
                zoneAction.accessKeySecret = platform.getPassword();
            }
            List<?> regionList = zoneAction.call().value.inventories;

            // 查询虚拟机列表
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }
            QueryVmInstanceAction.Result res = action.call();

            if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
                for (Object vm : res.value.inventories) {
                    JsonObject jsonObject = GsonUtil.GSON.toJsonTree(vm).getAsJsonObject();
                    JsonElement hypervisorTypeElement = jsonObject.get("hypervisorType");
                    String hypervisorType = hypervisorTypeElement != null && !hypervisorTypeElement.isJsonNull() ?
                            hypervisorTypeElement.getAsString() : "";
                    String uuid = getStringFromJson(jsonObject, "uuid", "");
                    if (uuid != null && !uuid.isEmpty() && KVM.equals(hypervisorType)) {
                        processVmData(jsonObject, platform, token, regionList, vmDataList);
                    }
                }
            }

            // 每个平台的数据收集完成后立即发送
            if (!vmDataList.isEmpty()) {
                ClusterMsg.Message.Builder platformMessage = ClusterMsg.Message.newBuilder();
                BasicCollectData build = BasicCollectData.builder()
                        .basicDataMap(vmDataList)
                        .metricsName(BASIC_VM.code())
                        .build();
                platformMessage.setType(ClusterMsg.MessageType.BASIC);
                platformMessage.setData(GsonUtil.GSON.toJson(build));
                platformMessage.setTime(System.currentTimeMillis());
                // 发送该平台的数据
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), platformMessage.build());
            }

            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });

        }
    }

    private void processVmData(JsonObject jsonObject, Platform platform, String token, List regionList, List<VmData> vmDataList) {
        try {
            VmData vmData = new VmData();
            AtomicReference<String> clusterName = new AtomicReference<>("");
            AtomicReference<String> hardwareName = new AtomicReference<>("");
            // 获取基本信息
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            String name = getStringFromJson(jsonObject, "name", "-");
            String state = getStringFromJson(jsonObject, "state", "-");
            String imageUuid = getStringFromJson(jsonObject, "imageUuid", "");
            String clusterUuid = getStringFromJson(jsonObject, "clusterUuid", "");
            String hardwareUuid = getStringFromJson(jsonObject, "lastHostUuid", "");
            String zoneUuid = getStringFromJson(jsonObject, "zoneUuid", "");
            String type = getStringFromJson(jsonObject, "type", "-");
            if (type.equals("ApplianceVm")) {
                return;
            }
            Long memorySize = getLongFromJson(jsonObject, "memorySize");
            Integer cpuNum = getIntFromJson(jsonObject, "cpuNum");
            // 默认值设置
            String ip = "";
            String vip_ip = "";
            String mac = "";
            String architecture = jsonObject.get("architecture").getAsString();
            String guestOsType = jsonObject.get("guestOsType").getAsString();
            String zoneName = "";
            // 创建日期处理
            String createDateStr = getStringFromJson(jsonObject, "createDate", "");
            Date createDate = new Date();
            if (!createDateStr.isEmpty()) {
                createDate = new Date(createDateStr);
            }
            // 获取各种指标数据
            BigDecimal cpu_useds = getMetricData("ZStack/VM", "CPUAverageUsedUtilization", VM_UUID_PREFIX, uuid, token, platform);
            BigDecimal memory_useds = getMetricData("ZStack/VM", "MemoryUsedInPercent", VM_UUID_PREFIX, uuid, token, platform);
            BigDecimal inBytes = getMetricData("ZStack/VM", "NetworkAllInBytes", VM_UUID_PREFIX, uuid, token, platform);
            BigDecimal outBytes = getMetricData("ZStack/VM", "NetworkAllOutBytes", VM_UUID_PREFIX, uuid, token, platform);
            BigDecimal inPackets = getMetricData("ZStack/VM", "NetworkAllInPackets", VM_UUID_PREFIX, uuid, token, platform);
            BigDecimal outPackets = getMetricData("ZStack/VM", "NetworkAllOutPackets", VM_UUID_PREFIX, uuid, token, platform);
            // 磁盘相关数据初始化
            BigDecimal actualSize = ZERO;
            BigDecimal cloudSize = ZERO;
            BigDecimal diskFreeBytes = ZERO;
            BigDecimal diskUsed = ZERO;
            // 处理云盘数据
            JsonArray jsonArrays = jsonObject.getAsJsonArray("allVolumes");
            if (jsonArrays != null && !jsonArrays.isEmpty()) {
                for (int i = 0; i < jsonArrays.size(); i++) {
                    JsonObject volumeObj = jsonArrays.get(i).getAsJsonObject();
                    // 处理Root盘
                    if ("Root".equals(getStringFromJson(volumeObj, "type", ""))) {
                        actualSize = getBigDecimalFromJson(volumeObj, "actualSize", ZERO);
                        cloudSize = getBigDecimalFromJson(volumeObj, "size", ZERO);
                        diskFreeBytes = cloudSize.subtract(actualSize);
                        diskUsed = actualSize.divide(cloudSize, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
            // 获取区域名称
            for (Object item : regionList) {
                if (item instanceof ZoneInventory) {
                    ZoneInventory zone = (ZoneInventory) item;
                    if (zone.getUuid().equals(zoneUuid)) {
                        zoneName = zone.getName();
                        break;
                    }
                }
            }
            // 获取集群名称
            QueryClusterAction clusters = new QueryClusterAction();
            clusters.conditions = List.of("hypervisorType=" + KVM);
            if (platform.getAkType() == 0) {
                clusters.sessionId = token;
            } else {
                clusters.accessKeyId = platform.getUsername();
                clusters.accessKeySecret = platform.getPassword();
            }
            QueryClusterAction.Result clusterResult = clusters.call();
            if (clusterResult != null && clusterResult.value != null &&
                    CollUtil.isNotEmpty(clusterResult.value.inventories)) {
                for (Object cluster : clusterResult.value.inventories) {
                    JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                    if (clusterUuid.equals(getStringFromJson(clusterObj, "uuid", ""))) {
                        clusterName.set(getStringFromJson(clusterObj, "name", ""));
                        break;
                    }
                }
            }
            // 获取主机名称
            QueryHostAction hostAction = new QueryHostAction();
            if (platform.getAkType() == 0) {
                hostAction.sessionId = token;
            } else {
                hostAction.accessKeyId = platform.getUsername();
                hostAction.accessKeySecret = platform.getPassword();
            }
            QueryHostAction.Result hostResult = hostAction.call();
            if (hostResult != null && hostResult.value != null &&
                    CollUtil.isNotEmpty(hostResult.value.inventories)) {
                for (Object host : hostResult.value.inventories) {
                    JsonObject hostObj = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
                    if (hardwareUuid.equals(getStringFromJson(hostObj, "uuid", ""))) {
                        hardwareName.set(getStringFromJson(hostObj, "name", ""));
                        break;
                    }
                }
            }
            QueryEipAction eipAction = new QueryEipAction();
            if (platform.getAkType() == 0) {
                eipAction.sessionId = token;
            } else {
                eipAction.accessKeyId = platform.getUsername();
                eipAction.accessKeySecret = platform.getPassword();
            }
            List list = eipAction.call().value.inventories;
            // 弹性ip
            JsonArray jsonArray = jsonObject.getAsJsonArray("vmNics");
            if (!jsonArray.isEmpty()) {
                List<String> ipList = new ArrayList<>();
                List<String> macList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject object = jsonArray.get(i).getAsJsonObject();
                    JsonElement macElement = object.get("mac");
                    JsonElement ipElement = object.get("ip");
                    if (macElement != null && !macElement.isJsonNull()) {
                        macList.add(macElement.getAsString());
                    }
                    if (ipElement != null && !ipElement.isJsonNull()) {
                        ipList.add(ipElement.getAsString());
                    }
                }
                ip = CollUtil.join(ipList, ",");
                mac = CollUtil.join(macList, ",");
                if (ip.equals("null")) {
                    ip = "";
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject vm = jsonArray.get(i).getAsJsonObject();
                    for (Object eip : list) {
                        JsonObject jsonObject1 = GsonUtil.GSON.toJsonTree(eip).getAsJsonObject();
                        String eipUuid = jsonObject1.get("vmNicUuid").getAsString();
                        if (eipUuid.equals(vm.get("uuid").getAsString())) {
                            vip_ip = jsonObject1.get("vipIp").getAsString();
                        }
                    }
                }

            }
            //电源状态
            String powerState = switch (state) {
                case "Created" -> "on";
                case "Starting" -> "on";
                case "Running" -> "on";
                case "Stopping" -> "off";
                case "Stopped" -> "off";
                case "Unknown" -> "unknown";
                case "Rebooting" -> "on";
                case "Destroyed" -> "off";
                case "Destroying" -> "off";
                case "Migrating" -> "on";
                case "Expunging" -> "off";
                case "Paunging" -> "off";
                case "Paused" -> "off";
                case "Resuming" -> "on";
                case "VolumeMigrating" -> "on";
                default -> "unknown";
            };

            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            action.uuid = uuid;
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }
            GetVmGuestToolsInfoAction.Result res = action.call();
            if (res.value != null) {
                boolean toolsRunning = "Running".equals(res.value.status);
                vmData.setToolsInstalled(String.valueOf(toolsRunning));
                vmData.setToolsRunStatus(toolsRunning ? "run" : "stop");

                if (toolsRunning) {
                    GetMetricDataAction metricDataAction = new GetMetricDataAction();
                    metricDataAction.namespace = "ZStack/VM";
                    metricDataAction.metricName = "DiskUsedCapacityInPercent";
                    metricDataAction.labels = List.of("VMUuid=" + uuid);
                    if (platform.getAkType() == 0) {
                        metricDataAction.sessionId = token;
                    } else {
                        metricDataAction.accessKeyId = platform.getUsername();
                        metricDataAction.accessKeySecret = platform.getPassword();
                    }
                    long now = System.currentTimeMillis() / 1000;
                    metricDataAction.startTime = now - 60;
                    metricDataAction.endTime = now;

                    GetMetricDataAction.Result result = metricDataAction.call();
                    if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                        JsonArray metricArray = GsonUtil.GSON.toJsonTree(result.value.data).getAsJsonArray();
                        for (int i = metricArray.size() - 1; i >= 0; i--) {
                            JsonObject entry = metricArray.get(i).getAsJsonObject();
                            JsonObject labels = entry.getAsJsonObject("labels");
                            boolean isRootFs = "/".equals(getStringFromJson(labels, "MountPoint", ""))
                                    && "rootfs".equals(getStringFromJson(labels, "DiskDeviceLetter", ""));
                            if (isRootFs) {
                                diskUsed = getBigFromJson(entry, "value");
                                break;
                            }
                        }
                    }
                }
            }

            QueryUserTagAction actionTag = new QueryUserTagAction();
            actionTag.conditions = asList("resourceUuid=" + uuid, "resourceType=VmInstanceVO");
            if (platform.getAkType() == 0) {
                actionTag.sessionId = token;
            } else {
                actionTag.accessKeyId = platform.getUsername();
                actionTag.accessKeySecret = platform.getPassword();
            }
            QueryUserTagAction.Result restag = actionTag.call();

            List<String> tagValues = new ArrayList<>();
            for (Object inventory : restag.value.inventories) {
                JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject().getAsJsonObject("tagPattern");
                String tag = getStringFromJson(tagDO, "name", "");
                String taguuid = getStringFromJson(tagDO, "uuid", "");
                if (StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)) {
                    tagValues.add(tag + "&" + taguuid);
                }
            }

            String resultTag = tagValues.isEmpty() ? "" : (tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));

            // 设置VmData属性
            vmData.setPowerState(powerState);
            vmData.setDeleted(0);
            vmData.setUuid(uuid);
            vmData.setName(name);
            vmData.setState(state);
            vmData.setIp(ip);
            vmData.setVipIp(vip_ip);
            vmData.setZoneUuid(zoneUuid);
            vmData.setClusterUuid(clusterUuid);
            vmData.setImageUuid(imageUuid);
            vmData.setHardwareUuid(hardwareUuid);
            vmData.setArchitecture(architecture);
            vmData.setGuestOsType(guestOsType);
            vmData.setVCreateDate(DateUtil.date(createDate));
            vmData.setCpuUsed(cpu_useds);
            vmData.setMemoryUsed(memory_useds);
            vmData.setZoneName(zoneName);
            vmData.setClusterName(clusterName.toString());
            vmData.setHardwareName(hardwareName.toString());
            vmData.setType(type);
            vmData.setMemorySize(StringUtil.toLong(memorySize));
            vmData.setCpuNum(StringUtil.toInt(cpuNum));
            vmData.setDiskUsed(diskUsed);
            vmData.setTag(resultTag);
            // 处理MAC地址
            String trimmedMac = StringUtil.toString(mac);
            if (trimmedMac == null || trimmedMac.isEmpty()) {
                vmData.setMac("-");
            } else {
                if (trimmedMac.contains(",")) {
                    // 如果包含逗号，取第一个MAC地址
                    vmData.setMac(trimmedMac.split(",")[0].trim());
                } else {
                    // 如果不包含逗号，直接返回
                    vmData.setMac(trimmedMac);
                }
            }
            // 设置平台相关信息
            vmData.setRegionId(platform.getRegionId());
            vmData.setPlatformId(platform.getPlatformId());
            vmData.setPlatformName(platform.getPlatformName());
            // 设置磁盘相关信息
            vmData.setActualSize(actualSize);
            vmData.setCloudSize(cloudSize);
            vmData.setNetworkInBytes(inBytes);
            vmData.setNetworkOutBytes(outBytes);
            vmData.setDiskUsedBytes(actualSize);
            vmData.setNetworkInPackets(inPackets);
            vmData.setNetworkOutPackets(outPackets);
            vmData.setDiskFreeBytes(diskFreeBytes);
            vmData.setTotalDiskCapacity(cloudSize);
            vmData.setTypeName("zstack");
            // 添加到列表
            vmDataList.add(vmData);
        } catch (Exception e) {
            log.error("处理虚拟机数据时发生错误: {} 平台: {}", CommonUtil.getMessageFromThrowable(e), platform.getPlatformName());
        }
    }

    /**
     * 从JsonObject中安全获取BigDecimal值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JsonObject jsonObject, String key, BigDecimal defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsBigDecimal();
        }
        return defaultValue;
    }

    /**
     * 获取指标数据的通用方法
     *
     * @param namespace  命名空间
     * @param metricName 指标名称
     * @param uuidPrefix UUID前缀
     * @param uuid       UUID
     * @param token      会话令牌
     * @return 指标值
     */
    private BigDecimal getMetricData(String namespace, String metricName, String uuidPrefix, String uuid, String token, Platform platform) {
        try {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = asList(uuidPrefix + uuid);
            if (platform.getAkType() == 0) {
                action.sessionId = token;
            } else {
                action.accessKeyId = platform.getUsername();
                action.accessKeySecret = platform.getPassword();
            }

            GetMetricDataAction.Result result = action.call();
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                return dataObj.get("value").getAsBigDecimal();
            }
        } catch (Exception e) {
            log.error("获取指标数据失败: namespace={}, metricName={}, uuid={}", namespace, metricName, uuid, e);
        }
        return ZERO;
    }

    /**
     * 获取资源配置的通用方法
     *
     * @param category     类别
     * @param name         名称
     * @param resourceUuid 资源UUID
     * @param token        会话令牌
     * @return 配置值
     */
    private String getResourceConfig(String category, String name, String resourceUuid, String token) {
        try {
            GetResourceConfigAction action = new GetResourceConfigAction();
            action.category = category;
            action.name = name;
            action.resourceUuid = resourceUuid;
            action.sessionId = token;

            GetResourceConfigAction.Result result = action.call();
            if (result != null && result.value != null) {
                return result.value.value;
            }
        } catch (Exception e) {
            log.error("获取资源配置失败: category={}, name={}, resourceUuid={}", category, name, resourceUuid, e);
        }
        return null;
    }

    /**
     * 从JsonObject中安全获取字符串值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return 字符串值
     */
    private String getStringFromJson(JsonObject jsonObject, String key, String defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsString();
        }
        return defaultValue;
    }

    /**
     * 从JsonObject中安全获取长整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 长整型值
     */
    private Long getLongFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        }
        return 0L;
    }

    /**
     * 从JsonObject中安全获取整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 整型值
     */
    private Integer getIntFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsInt();
        }
        return 0;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_VM.code();
    }
}
