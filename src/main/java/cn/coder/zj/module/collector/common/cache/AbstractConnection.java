package cn.coder.zj.module.collector.common.cache;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractConnection<T> implements AutoCloseable{
    /**
     * @return Returns the connection.
     */
    public abstract T getConnection();

    /**
     * Close connection
     */
    public abstract void closeConnection() throws Exception;

    @Override
    public void close() throws Exception{
        closeConnection();
    }
}
