package cn.coder.zj.module.collector.collect.token.vmware;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.vmware.VmwarePlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.mo.ServiceInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static cn.coder.zj.module.collector.enums.PlatformType.VM_WARE;

@Slf4j
public class VmWareTokenImpl extends AbstractToken {

    @Override
    public void preCheck(Platform platform) {
        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
        final int MAX_ATTEMPTS = 3;
        int successCount = 0;
        int failureCount = 0;

        log.info("开始对平台 {} 进行连接检查，将尝试 {} 次", platform.getPlatformName(), MAX_ATTEMPTS);

        for (int attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
            int finalAttempt = attempt;
            FutureTask<Boolean> task = new FutureTask<>(() -> {
                // VMware SDK可能也存在全局状态问题，使用同步保护
                synchronized (VmWareTokenImpl.class) {
                    try {
                        // 使用更宽松的超时设置进行连接测试
                        ServiceInstance serviceInstance = createServiceInstance(platform.getPlatformUrl(), platform.getUsername(), platform.getPassword());
                        if (serviceInstance != null) {
                            // 验证连接是否真正可用
                            serviceInstance.getAboutInfo();
                            log.debug("平台 {} 第{}次尝试验证通过", platform.getPlatformName(), finalAttempt);
                            // 主动关闭测试连接，避免连接泄露
                            try {
                                serviceInstance.getServerConnection().logout();
                            } catch (Exception e) {
                                log.debug("关闭测试连接时出现异常，可忽略: {}", e.getMessage());
                            }
                            return true;
                        }
                        log.debug("平台 {} 第{}次尝试创建ServiceInstance失败", platform.getPlatformName(), finalAttempt);
                        return false;
                    } catch (Exception e) {
                        log.debug("平台 {} 第{}次尝试异常: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                        return false;
                    }
                }
            });

            try {
                asyncTaskExecutor.submit(task);
                // 增加超时时间到60秒，适应网络波动和服务器负载情况
                boolean isConnected = task.get(20, TimeUnit.SECONDS);

                if (isConnected) {
                    successCount++;
                    log.info("平台 {} 第{}次连接检查成功", platform.getPlatformName(), attempt);
                } else {
                    failureCount++;
                    log.warn("平台 {} 第{}次连接检查失败", platform.getPlatformName(), attempt);
                }

            } catch (TimeoutException e) {
                failureCount++;
                log.error("平台 {} 第{}次连接检查超时(60秒)", platform.getPlatformName(), attempt);
                task.cancel(true);
            } catch (Exception e) {
                failureCount++;
                log.error("平台 {} 第{}次连接检查异常: {}", platform.getPlatformName(), attempt, e.getMessage());
                task.cancel(true);
            }

            // 继续所有尝试，不提前退出
            if (attempt < MAX_ATTEMPTS) {
                try {
                    // 减少重试间隔，提高响应速度
                    Thread.sleep(10000); // 等待10秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 根据最终结果判断平台状态
        boolean finalResult = successCount > 0; // 只要有一次成功就认为平台可用

        if (finalResult) {
            log.info("平台 {} 连接检查完成: 成功 {}/{} 次，判定为在线",
                    platform.getPlatformName(), successCount, MAX_ATTEMPTS);
        } else {
            log.error("平台 {} 连接检查完成: 失败 {}/{} 次，判定为离线",
                    platform.getPlatformName(), failureCount, MAX_ATTEMPTS);
        }

        // 【关键修复】移除preCheck中的状态更新，避免与token方法的状态更新冲突
        // preCheck只负责连接验证，最终状态由token方法决定
        // 在所有尝试结束后更新平台状态
        updatePlatformStatus(platform, finalResult);
        log.info("平台 {} preCheck验证完成，等待token方法完成最终状态确认", platform.getPlatformName());
    }

    @Override
    public void token(Platform platform) {
        ServiceInstance serviceInstance = null;
        try {
            log.info("开始为平台 {} 获取token", platform.getPlatformName());
            serviceInstance = createServiceInstance(
                    platform.getPlatformUrl(),
                    platform.getUsername(),
                    platform.getPassword()
            );

            if (serviceInstance == null) {
                log.error("平台 {} 登录失败: ServiceInstance创建失败", platform.getPlatformName());

                return;
            }
            
            // 验证连接是否真正可用
            try {
                serviceInstance.getAboutInfo();
                log.info("平台 {} ServiceInstance连接验证成功", platform.getPlatformName());
            } catch (Exception e) {
                log.error("平台 {} ServiceInstance连接验证失败: {}", platform.getPlatformName(), e.getMessage());

                return;
            }

            // 构建平台对象并缓存
            Platform platform1 = platform;
            platform1.setVmWarePlatform(VmwarePlatform.builder().serviceInstance(serviceInstance).build());
            Map<String, Object> map = new HashMap<>();
            map.put(platform.getPlatformId().toString(), platform1);
            CacheService.put(VM_WARE.code(), map);
            
            log.info("平台 {} token获取成功，已缓存ServiceInstance", platform.getPlatformName());


        } catch (RemoteException e) {
            log.error("平台 {} 远程连接异常: {}", platform.getPlatformName(), e.getMessage());

        } catch (MalformedURLException e) {
            log.error("平台 {} URL格式错误: {}", platform.getPlatformName(), e.getMessage());

        } catch (Exception e) {
            log.error("平台 {} token获取异常: {}", platform.getPlatformName(), e.getMessage(), e);

        }
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder()
                .setData(GsonUtil.GSON_EXCLUDE_VMWARE.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    public static ServiceInstance createServiceInstance(String url, String username, String password) throws RemoteException, MalformedURLException {
        // Disable SSL certificate verification
        try {
            disableSSLVerification();
            // 增加连接和读取超时时间，以适应网络波动和服务器负载情况
            // 连接超时：30秒，读取超时：90秒
            ServiceInstance si = new ServiceInstance(new URL(url + "/sdk"), username, password, true, 30000, 90000);
            si.getSessionManager().setLocale("zh-CN"); // set locale for the content of all API result.
            return si;
        } catch (Exception e) {
            throw new RuntimeException("创建VMware ServiceInstance失败: " + e.getMessage(), e);
        }
    }


    // Method to disable SSL certificate verification
    private static void disableSSLVerification() {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        } catch (Exception e) {
            log.error("disableSSLVerification error: {}", e.getMessage());
        }

    }

    @Override
    public String supportProtocol() {
        return VM_WARE.code();
    }


    // 提取host
    private String extractHost(String url) {
        try {
            java.net.URL u = new java.net.URL(url);
            return u.getHost();
        } catch (Exception e) {
            return url;
        }
    }
    // 提取port，若无则用默认443
    private int extractPort(String url) {
        try {
            java.net.URL u = new java.net.URL(url);
            int port = u.getPort();
            return port > 0 ? port : 443;
        } catch (Exception e) {
            return 443;
        }
    }

}
