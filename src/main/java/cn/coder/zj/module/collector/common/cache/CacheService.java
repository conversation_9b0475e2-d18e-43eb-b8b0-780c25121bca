package cn.coder.zj.module.collector.common.cache;

import cn.coder.zj.module.collector.util.GsonUtil;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 全局缓存服务类
 * <AUTHOR>
 */
@Slf4j
@Component
public class CacheService {

    private static final ConcurrentHashMap<String, Map<String, Object>> CACHE = new ConcurrentHashMap<>();

    private static final ConcurrentHashMap<String, ChannelHandlerContext> CONTEXT = new ConcurrentHashMap<>();

    /**
     * 添加数据到缓存
     *
     * @param key1  一级缓存键
     * @param key2  二级缓存键
     * @param value 缓存的值
     */
    public static void put(String key1, String key2, Object value) {
        Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(key2)
                        .flatMap(k2 -> Optional.ofNullable(value)
                                .map(v -> {
                                    CACHE.computeIfAbsent(k1, k -> new ConcurrentHashMap<>()).put(k2, v);
                                    log.info("Data added to cache: key1={}, key2={}, value={}", k1, k2, v);
                                    return v;
                                })))
                .orElseGet(() -> {
                    log.error("Key1, key2 or value is null, put operation ignored.");
                    return null;
                });
    }


    /**
     * 添加整个二级Map到缓存
     *
     * @param key1    一级缓存键
     * @param subMap  二级缓存Map
     */
    public static void put(String key1, Map<String, Object> subMap) {
        Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(subMap)
                        .map(map -> {
                            CACHE.compute(k1, (k, existingMap) -> {
                                if (existingMap == null) {
                                    return new ConcurrentHashMap<>(map);
                                } else {
                                    existingMap.putAll(map);
                                    return existingMap;
                                }
                            });
                            log.info("SubMap merged to cache: key1={}, added entries={}", k1, map.size());
                            return map;
                        }))
                .orElseGet(() -> {
                    log.error("Key1 or subMap is null, put operation ignored.");
                    return null;
                });
    }

    /**
     * 替换整个二级Map到缓存（不合并，直接替换）
     * 用于解决缓存累积问题
     *
     * @param key1    一级缓存键
     * @param subMap  二级缓存Map
     */
    public static void replace(String key1, Map<String, Object> subMap) {
        Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(subMap)
                        .map(map -> {
                            CACHE.put(k1, new ConcurrentHashMap<>(map));
                            log.info("SubMap replaced in cache: key1={}, entries={}", k1, map.size());
                            return map;
                        }))
                .orElseGet(() -> {
                    log.error("Key1 or subMap is null, replace operation ignored.");
                    return null;
                });
    }

    /**
     * 清除指定key1下的所有数据
     *
     * @param key1 一级缓存键
     */
    public static void clearKey1(String key1) {
        Optional.ofNullable(key1)
                .map(k1 -> {
                    Map<String, Object> removed = CACHE.remove(k1);
                    if (removed != null) {
                        log.info("Cleared cache for key1={}, removed entries={}", k1, removed.size());
                        return true;
                    } else {
                        log.info("No cache found for key1={}", k1);
                        return false;
                    }
                })
                .orElseGet(() -> {
                    log.error("Key1 is null, clear operation ignored.");
                    return false;
                });
    }

    /**
     * 从缓存中获取数据
     *
     * @param key1 一级缓存键
     * @param key2 二级缓存键
     * @return 缓存的值
     */
    public static String get(String key1, String key2) {
        return String.valueOf(Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(key2)
                        .flatMap(k2 -> Optional.ofNullable(CACHE.get(k1))
                                .map(subMap -> subMap.get(k2))))
                .orElse(null));
    }

    /**
     * 删除缓存中的某个键
     *
     * @param key1 一级缓存键
     * @param key2 二级缓存键
     */
    public static void remove(String key1, String key2) {
        Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(key2)
                        .map(k2 -> {
                            Map<String, Object> subMap = CACHE.get(k1);
                            if (subMap != null) {
                                subMap.remove(k2);
                                log.info("Data removed from cache: key1={}, key2={}", k1, k2);
                                if (subMap.isEmpty()) {
                                    CACHE.remove(k1);
                                    log.info("Empty submap removed: key1={}", k1);
                                }
                                return true;
                            }
                            return false;
                        }))
                .orElseGet(() -> {
                    log.error("Key1 or key2 is null, remove operation ignored.");
                    return false;
                });
    }

    /**
     * 检查键是否存在
     *
     * @param key1 一级缓存键
     * @param key2 二级缓存键
     * @return 是否存在
     */
    public static boolean containsKey(String key1, String key2) {
        return Optional.ofNullable(key1)
                .flatMap(k1 -> Optional.ofNullable(key2)
                        .map(k2 -> {
                            Map<String, Object> subMap = CACHE.get(k1);
                            boolean exists = subMap != null && subMap.containsKey(k2);
                            log.info("Contains key check: key1={}, key2={}, exists={}", k1, k2, exists);
                            return exists;
                        }))
                .orElseGet(() -> {
                    log.error("Key1 or key2 is null, containsKey operation ignored.");
                    return false;
                });
    }
    /**
     * 获取指定key1下的所有值
     *
     * @param key1 一级缓存键
     * @return 值列表
     */
    public static List<Object> getValuesByKey1(String key1) {
        return Optional.ofNullable(key1)
                .map(k1 -> {
                    Map<String, Object> subMap = CACHE.get(k1);
                    if (subMap != null) {
                        List<Object> values = new ArrayList<>(subMap.values());
                        return values;
                    }
                    return new ArrayList<>();
                })
                .orElseGet(() -> {
                    log.error("Key1 is null, get values operation ignored.");
                    return new ArrayList<>();
                });
    }

    public static List<String> getAllKey1() {
        List<String> keys = new ArrayList<>(CACHE.keySet());
        log.info("Retrieved all key1s, count={}", keys.size());
        return keys;
    }


    /**
     * 存入ctx上下文
     * @param key
     * @return
     */
    public static void putCtx(String key, ChannelHandlerContext ctx) {
        CONTEXT.put(key,ctx);
    }

    /**
     * 获取ctx上下文
     * @param key
     * @return
     */
    public static ChannelHandlerContext getCtx(String key) {
        return CONTEXT.get(key);
    }


}
