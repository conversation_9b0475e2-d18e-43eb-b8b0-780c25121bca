package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_VM;

@Slf4j
public class InspurBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云云主机采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_LIST;
        String hostUrl = platform.getPlatformUrl() + InSpurApiConstant.GET_HOST_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",platform.getInSpurPlatform().getToken()
        );

        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        JsonArray hostArray = hostdata.getAsJsonArray("items");

        Map<String, JsonObject> hostMap = StreamSupport.stream(hostArray.spliterator(), false)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "id"),
                        host -> host,
                        (existing, replacement) -> existing
                ));

        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VmData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    VmData vmData = collectVmInfo(platform, jsonElement,hostMap,header);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理InSpur云盘数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private VmData collectVmInfo(Platform platform, JsonElement jsonElement,Map<String, JsonObject> hostMap,Map<String, String> header) {
        VmData vmData = new VmData();

        // 基础信息获取
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject hostId = hostMap.get(getStringFromJson(cloud, "hostId"));

        vmData.setClusterName(Optional.ofNullable(getStringFromJson(hostId, "clusterName")).orElse(getStringFromJson(hostId, "hostIp")));
        vmData.setClusterUuid(Optional.ofNullable(getStringFromJson(hostId, "clusterUrn")).orElse(getStringFromJson(hostId, "hostId")));

        // 基本信息设置
        vmData.setGuestOsType(getStringFromJson(cloud, "guestosLabel"));
        vmData.setHardwareUuid(getStringFromJson(cloud,"hostId"));
        vmData.setHardwareName(getStringFromJson(cloud,"hostName"));
        vmData.setDeleted(getBooleanFromJson(cloud, "template") ? 1 : 0);
        vmData.setUuid(getStringFromJson(cloud, "id"));
        vmData.setName(getStringFromJson(cloud, "name", "-"));
        vmData.setVipIp("");
        vmData.setToolsInstalled(getStringFromJson(cloud, "toolsInstalled"));
        vmData.setToolsRunStatus(stateConvert(getStringFromJson(cloud, "toolsRunningStatus")));

        // 性能指标获取
        BigDecimal[] metrics = {BigDecimal.ZERO, BigDecimal.ZERO};

        // 网络信息设置
        JsonArray nics = cloud.getAsJsonArray("nics");
        if (nics != null && !nics.isEmpty()) {
            JsonObject nic = nics.get(0).getAsJsonObject();
            vmData.setIp(getStringFromJson(nic, "ip"));
            vmData.setMac(getStringFromJson(nic, "mac"));
            metrics[0] = getBigFromJson(nic,"readBytes");
            metrics[1] = getBigFromJson(nic,"writeBytes");
        } else {
            vmData.setMac("");
        }

        // 硬件和状态信息设置
        vmData.setArchitecture(getStringFromJson(cloud, "cpuArchType"));

        String state = getStringFromJson(cloud, "status", "-");
        vmData.setState(StateUtil.stateConvert(state));
        vmData.setPowerState(StateUtil.powerStateConvert(state));

        boolean hasClusterId = hostId.has("dataCenterName") && StrUtil.isNotEmpty(getStringFromJson(hostId, "dataCenterName"));
        vmData.setZoneName(hasClusterId ?
                getStringFromJson(hostId, "dataCenterName") :
                getStringFromJson(hostId, "ip"));

        // HA和BIOS配置设置
        boolean haEnabled = getBooleanFromJson(cloud, "haEnabled");
        vmData.setAutoInitType(haEnabled ? "NeverStop" : "None");
        vmData.setGuideMode(getStringFromJson(cloud, "bootMode").equals("BIOS") ? "Legacy" : "UEFI");

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_INFO;
        JsonObject vmdata = FsApiCacheService.getJsonObject(url.replace("{id}",getStringFromJson(cloud, "id")), null, header);
        // 磁盘信息处理
        Long[] sizes = {0L, 0L};
        BigDecimal diskUsage = BigDecimal.ZERO;
        JsonArray disks = vmdata.getAsJsonArray("disks");
        for (JsonElement diskElement : disks) {
            JsonObject volume = diskElement.getAsJsonObject().getAsJsonObject("volume");
            if (getBooleanFromJson(volume, "bootable")) {
                Long capacity = getLongFromJsonDouble(volume, "sizeInByte");
                Long allocation = getLongFromJsonDouble(volume, "realSizeInByte");

                sizes[0] = capacity;
                sizes[1] = allocation;

                if (allocation != null && allocation > 0) {
                    diskUsage = BigDecimal.valueOf(allocation).divide(BigDecimal.valueOf(capacity), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                }
                break;
            }
        }

        // CPU和内存信息设置
        vmData.setCpuNum(getIntFromJson(cloud, "cpuNum"));
        vmData.setMemorySize(getLongFromJson(cloud, "memoryInByte"));

        // 设置所有指标
        vmData.setMemoryUsed(getBigFromJson(cloud,"memoryUsage"));
        vmData.setCpuUsed(getBigFromJson(cloud,"cpuUsage"));

        if(vmData.getToolsInstalled().equals("true") && vmData.getToolsRunStatus().equals("run")){
            vmData.setDiskUsed(getBigFromJson(cloud,"diskUsage"));
        }else {
            vmData.setDiskUsed(diskUsage);
        }
        String createTime = getStringFromJson(cloud, "createTime");
        try {
            vmData.setVCreateDate(StrUtil.isNumeric(createTime)
                    ? DateUtil.date(Long.parseLong(createTime) * 1000L)
                    : DateUtil.parse(createTime, "yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            vmData.setVCreateDate(DateUtil.date());
        }

        vmData.setRegionId(platform.getRegionId());
        vmData.setPlatformId(platform.getPlatformId());
        vmData.setPlatformName(platform.getPlatformName());
        vmData.setType("普通虚拟机");
        vmData.setTypeName(platform.getTypeCode());
        vmData.setTotalDiskCapacity(new BigDecimal(sizes[0]));
        vmData.setDiskUsedBytes(new BigDecimal(sizes[1]));
        vmData.setDiskFreeBytes(new BigDecimal(sizes[0] - sizes[1]));
        vmData.setCloudSize(new BigDecimal(sizes[0]));
        vmData.setActualSize(new BigDecimal(sizes[1]));
        vmData.setNetworkInBytes(metrics[0]);
        vmData.setNetworkOutBytes(metrics[1]);

        return vmData;
    }

    private String stateConvert(String status) {
        if (status.equals("STOPPED")) {
            return "stop";
        }
        return "run";
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_VM.code();
    }
}
