package cn.coder.zj.module.collector.collect.token.sangfor;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.dal.platform.sxf.SxfPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import javax.crypto.Cipher;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import static cn.coder.zj.module.collector.enums.PlatformType.SANG_FOR;

@Slf4j
public class SangforTokenImpl extends AbstractToken {

    private static final String GET_PUBLIC_KEY = "/vapi/json/public_key";
    private static final String GET_SANGFOR_LOGIN = "/vapi/json/access/ticket";
    private static final String RSA_EXPONENT = "10001";
    private static final String RSA_ALGORITHM = "RSA";
    private static final Gson GSON = new Gson();

    @Override
    public void preCheck(Platform platform) {
        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
        final int MAX_ATTEMPTS = 3;
        int successCount = 0;
        int failureCount = 0;

        log.info("开始对平台 {} 进行连接检查，将尝试 {} 次", platform.getPlatformName(), MAX_ATTEMPTS);

        for (int attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
            int finalAttempt = attempt;
            FutureTask<Boolean> task = new FutureTask<>(() -> {
                // 深信服平台可能也存在全局状态问题，使用同步保护
                synchronized (SangforTokenImpl.class) {
                    CompletableFuture<Boolean> future = new CompletableFuture<>();

                    OkHttpService.get(platform.getPlatformUrl() + GET_PUBLIC_KEY, null, null, new Callback() {
                        @Override
                        public void onFailure(Call call, IOException e) {
                            log.debug("平台 {} 第{}次尝试连接异常: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                            future.complete(false);
                        }

                        @Override
                        public void onResponse(Call call, Response response) throws IOException {
                            try (response) {
                                if (!response.isSuccessful()) {
                                    log.debug("平台 {} 第{}次尝试连接失败, 状态码: {}", platform.getPlatformName(), finalAttempt, response.code());
                                    future.complete(false);
                                    return;
                                }
                                String responseBody = response.body().string();
                                if (StringUtils.isEmpty(responseBody)) {
                                    log.debug("平台 {} 第{}次尝试响应为空", platform.getPlatformName(), finalAttempt);
                                    future.complete(false);
                                    return;
                                }

                                try {
                                    JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
                                    if (!jsonObject.has("data")) {
                                        log.debug("平台 {} 第{}次尝试响应格式错误: {}", platform.getPlatformName(), finalAttempt, responseBody);
                                        future.complete(false);
                                        return;
                                    }
                                    log.debug("平台 {} 第{}次尝试验证通过", platform.getPlatformName(), finalAttempt);
                                    future.complete(true);
                                } catch (Exception e) {
                                    log.debug("平台 {} 第{}次尝试JSON解析失败: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                                    future.complete(false);
                                }
                            }
                        }
                    });

                    return future.get(4, TimeUnit.SECONDS);
                }
            });

            try {
                asyncTaskExecutor.submit(task);
                boolean isConnected = task.get(30, TimeUnit.SECONDS);

                if (isConnected) {
                    successCount++;
                    log.info("平台 {} 第{}次连接检查成功", platform.getPlatformName(), attempt);
                } else {
                    failureCount++;
                    log.warn("平台 {} 第{}次连接检查失败", platform.getPlatformName(), attempt);
                }

            } catch (Exception e) {
                failureCount++;
                log.error("平台 {} 第{}次连接检查超时", platform.getPlatformName(), attempt);
                task.cancel(true);
            }

            // 继续所有尝试，不提前退出
            if (attempt < MAX_ATTEMPTS) {
                try {
                    log.debug("平台 {} 等待15秒后进行第{}次连接尝试", platform.getPlatformName(), attempt + 1);
                    Thread.sleep(15000); // 等待15秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 根据最终结果判断平台状态
        boolean finalResult = successCount > 0; // 只要有一次成功就认为平台可用

        if (finalResult) {
            log.info("平台 {} 连接检查完成: 成功 {}/{} 次，判定为在线",
                    platform.getPlatformName(), successCount, MAX_ATTEMPTS);
        } else {
            log.error("平台 {} 连接检查完成: 失败 {}/{} 次，判定为离线",
                    platform.getPlatformName(), failureCount, MAX_ATTEMPTS);
        }

        // 在所有尝试结束后更新平台状态
        updatePlatformStatus(platform, finalResult);
    }


    @Override
    public void token(Platform platform) {
        try {
            OkHttpService.get(platform.getPlatformUrl() + GET_PUBLIC_KEY, null, null, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("获取公钥失败: {}", e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (!response.isSuccessful()) {
                            log.error("获取公钥请求失败, 状态码: {}, 信息: {}", response.code(), response.message());
                            return;
                        }

                        String responseBody = response.body().string();
                        JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();

                        if (!jsonObject.has("data")) {
                            log.error("获取公钥响应数据格式错误: {}", responseBody);
                            return;
                        }

                        String publicKey = jsonObject.get("data").getAsString();
                        getSangforLoginToken(platform, publicKey);
                    } catch (Exception e) {
                        log.error("处理公钥响应数据失败: {}", e.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            log.error("token获取过程发生异常: {}", e.getMessage());
        }
    }

    private void getSangforLoginToken(Platform platform, String publicKey) {
        try {
            String encryptedPassword = encryptor(publicKey, platform.getPassword());
            Map<String, String> params = new HashMap<>();
            params.put("username", platform.getUsername());
            params.put("password", encryptedPassword);

            try (Response response = OkHttpService.postSync(platform.getPlatformUrl() + GET_SANGFOR_LOGIN, params)) {
                if (!response.isSuccessful()) {
                    log.error("平台 {} 登录失败, 状态码: {}", platform.getPlatformName(), response.code());

                    return;
                }

                String responseBody = response.body().string();
                JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
                if (!jsonObject.has("data") || !jsonObject.getAsJsonObject("data").has("ticket")) {
                    log.error("平台 {} 登录响应格式错误: {}", platform.getPlatformName(), responseBody);

                    return;
                }

                String cookie = jsonObject.getAsJsonObject("data").get("ticket").getAsString();
                String csrfToken = jsonObject.getAsJsonObject("data").get("CSRFPreventionToken").getAsString();
                List<MonitorInfo> vm = getUuids(platform, "VM", cookie, csrfToken);
                List<MonitorInfo> host = getUuids(platform, "HOST", cookie, csrfToken);
                platform.setSxfPlatform(SxfPlatform.builder()
                        .loginAuthCookie(cookie)
                        .vmUuids(vm)
                        .hostUuids(host)
                        .token(csrfToken)
                        .build());

                Map<String, Object> tokenMap = new HashMap<>();
                tokenMap.put(platform.getPlatformId().toString(), platform);
                CacheService.put(SANG_FOR.code(), tokenMap);
                log.info("平台 {} 登录成功，token已缓存", platform.getPlatformName());

            }
        } catch (Exception e) {
            log.error("平台 {} 登录异常: {}", platform.getPlatformName(), e.getMessage());

        }
    }

    private static String encryptor(String publicKey, String password) {
        try {
            byte[] encryptedBytes = encrypt(password.getBytes(StandardCharsets.UTF_8), publicKey);
            return bytesToHex(encryptedBytes);
        } catch (Exception e) {
            log.error("密码加密失败: {}", e.getMessage());
            throw new RuntimeException("Password encryption failed", e);
        }
    }

    private static RSAPublicKey getPublicKey(String modulus, String exponent) {
        try {
            BigInteger modBigInt = new BigInteger(modulus, 16);
            BigInteger expBigInt = new BigInteger(exponent, 16);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modBigInt, expBigInt);
            return (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("生成公钥失败: {}", e.getMessage());
            throw new RuntimeException("Failed to generate public key", e);
        }
    }

    private static byte[] encrypt(byte[] plaintext, String key) throws Exception {
        PublicKey publicKey = getPublicKey(key, RSA_EXPONENT);
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(plaintext);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexBuilder = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            hexBuilder.append(String.format("%02X", b));
        }
        return hexBuilder.toString();
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    public List<MonitorInfo> getUuids(Platform platform, String actionType, String cookie, String token) {
        List<MonitorInfo> uuidList = new ArrayList<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", "LoginAuthCookie=" + cookie);
        headers.put("CSRFPreventionToken", token);

        CompletableFuture<List<MonitorInfo>> future = new CompletableFuture<>();
        String url = platform.getPlatformUrl() +
                ("VM".equals(actionType) ? SangForApiConstant.GET_CLOUDS : SangForApiConstant.GET_HARDWARE);

        OkHttpService.get(url, null, headers, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.error("获取{}数据失败: {}", actionType, e.getMessage());
                future.complete(uuidList);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (response) {
                    String responseBody = response.body().string();
                    JsonArray dataArray = GSON.fromJson(responseBody, JsonElement.class)
                            .getAsJsonObject().get("data").getAsJsonArray();

                    for (JsonElement element : dataArray) {
                        JsonObject jsonObject = element.getAsJsonObject();
                        String id = "VM".equals(actionType) ?
                                jsonObject.get("vmid").getAsString() :
                                jsonObject.get("id").getAsString();
                        String name = jsonObject.get("name").getAsString();
                        uuidList.add(MonitorInfo.builder()
                                .name(name)
                                .uuid(id)
                                .build());
                    }
                    future.complete(uuidList);
                } catch (Exception e) {
                    log.error("处理{}数据失败: {}", actionType, e.getMessage());
                    future.complete(uuidList);
                }
            }
        });

        try {
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取{}数据超时: {}", actionType, e.getMessage());
            return uuidList;
        }
    }


    @Override
    public String supportProtocol() {
        return SANG_FOR.code();
    }
}
