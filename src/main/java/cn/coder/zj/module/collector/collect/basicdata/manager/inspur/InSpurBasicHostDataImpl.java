package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_HOST;

@Slf4j
public class InSpurBasicHostDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<HostData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云物理机采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostData> collectData(Platform platform) {
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String hostUrl = platform.getPlatformUrl() + InSpurApiConstant.GET_HOST_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",platform.getInSpurPlatform().getToken()
        );

        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(hostArray)) return null;

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<HostData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    HostData hostData = collectHostInfo(platform, jsonElement,header);
                    if (hostData != null) {
                        dataList.add(hostData);
                    }
                } catch (Exception e) {
                    log.error("处理浪潮云物理机数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private HostData collectHostInfo(Platform platform, JsonElement jsonElement, Map<String, String> header) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject hostBase = GSON.toJsonTree(jsonElement).getAsJsonObject();
        if (ObjectUtil.isNull(hostBase)) return null;

        String hostId = getStringFromJson(hostBase, "id");
        JsonObject hostInfo = getJsonObjectFromFsApi(platformUrl + "/hosts/" + hostId, null, header);

        // 初始化网络带宽数据
        String inBps = "0", outBps = "0";
        Map<String, String> params = Map.of(
                "targetType", "HOST",
                "type", "0",
                "itemIns", "interface_kbps",
                "objuuid", hostId,
                "section","10"
        );

        // 获取网络带宽数据
        JsonArray hostArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, header);
        for (JsonElement element : hostArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");

            if (perfPoints != null && perfPoints.size() > 0) {
                JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                if ("interface_kbps-read".equals(key)) {
                    inBps = getStringFromJson(lastPoint, "data");
                } else if ("interface_kbps-write".equals(key)) {
                    outBps = getStringFromJson(lastPoint, "data");
                }
            }
        }

        // 创建并填充主机数据对象
        HostData hostData = new HostData();
        hostData.setCpuOverPercent(BigDecimal.ONE);
        hostData.setMemoryOverPercent(BigDecimal.ONE);

        // 设置基本信息
        hostData.setName(getStringFromJson(hostInfo, "hostName"));
        hostData.setUuid(hostId);
        hostData.setIp(getStringFromJson(hostInfo, "ip"));
        hostData.setStatus(stateConvert(getStringFromJson(hostBase, "status")));
        hostData.setState("Enabled");

        // 设置集群信息
        hostData.setClusterName(Optional.ofNullable(getStringFromJson(hostBase, "clusterName")).orElse(getStringFromJson(hostBase, "hostIp")));
        hostData.setClusterUuid(Optional.ofNullable(getStringFromJson(hostBase, "clusterUrn")).orElse(getStringFromJson(hostBase, "hostId")));

        // 设置可用区
        boolean hasClusterId = hostBase.has("dataCenterName") && StrUtil.isNotEmpty(getStringFromJson(hostBase, "dataCenterName"));
        hostData.setAvailableManager(hasClusterId ?
                getStringFromJson(hostBase, "dataCenterName") :
                getStringFromJson(hostInfo, "ip"));

        // 设置管理器和CPU类型
        hostData.setManager(platform.getPlatformName());
        hostData.setCpuType(getStringFromJson(hostBase, "cpuType"));

        // 设置内存预留
        hostData.setReservedMemory(new BigDecimal(0));

        // 设置硬件信息
        hostData.setModel(getStringFromJson(hostInfo, "model"));
        hostData.setSerialNumber(getStringFromJson(hostInfo, "serialNumber"));
        hostData.setBrandName(getStringFromJson(hostInfo, "manufacturer"));

        // 设置CPU信息
        long logicCpuNum = getLongFromJson(hostInfo, "logicCpuNum");
        long usedVCpus = getLongFromJson(hostInfo, "usedVCpus");
        hostData.setCpuNum(getIntFromJson(hostInfo, "logicCpuNum"));
        hostData.setTotalCpuCapacity(logicCpuNum);
        hostData.setCpuCommitRate(new BigDecimal(usedVCpus));
        hostData.setAvailableCpuCapacity(logicCpuNum - usedVCpus);
        hostData.setCpuSockets(getIntFromJson(hostInfo, "cpuSocket"));
        hostData.setCpuUsed(getBigFromJson(hostInfo, "cpuUsage"));
        hostData.setArchitecture(getStringFromJson(hostInfo, "cpuArchType"));

        // 设置平台信息
        hostData.setPlatformId(platform.getPlatformId());
        hostData.setPlatformName(platform.getPlatformName());
        hostData.setRegionId(platform.getRegionId());
        hostData.setTypeName(platform.getTypeCode());
        hostData.setDeleted(0);

        // 设置内存信息
        long totalMemInByte = getLongFromJsonDouble(hostInfo, "totalMemInByte");
        hostData.setTotalMemoryCapacity(totalMemInByte);
        hostData.setAvailableMemoryCapacity(getLongFromJsonDouble(hostInfo, "freeMemoryInByte"));
        hostData.setMemoryUsed(getBigFromJson(hostInfo, "memoryUsage"));
        hostData.setMemoryCommitRate(getBigFromJson(hostInfo,"logicUsedMemoryInByte"));
        hostData.setTotalVirtualMemory(getBigFromJson(hostInfo, "totalMemInByte"));

        // 设置磁盘信息
        BigDecimal totalDiskCapacity = getBigFromJson(hostInfo, "totalLocalDatastoreCapacityInByte");
        BigDecimal usedDiskCapacity = getBigFromJson(hostInfo, "usedLocalDatastoreCapacityInByte");
        hostData.setDiskUsedBytes(usedDiskCapacity);
        hostData.setDiskFreeBytes(totalDiskCapacity.subtract(usedDiskCapacity));
        hostData.setDiskUsed(getBigFromJson(hostInfo, "localDataStoreUsage"));
        hostData.setTotalDiskCapacity(totalDiskCapacity);

        // 设置带宽信息
        BigDecimal inBpsBd = new BigDecimal(inBps);
        BigDecimal outBpsBd = new BigDecimal(outBps);
        hostData.setBandwidthDownstream(inBpsBd.compareTo(BigDecimal.ZERO) < 0 ?
                inBpsBd : inBpsBd.multiply(new BigDecimal(1024)).setScale(0, RoundingMode.DOWN));
        hostData.setBandwidthUpstream(outBpsBd.compareTo(BigDecimal.ZERO) < 0 ?
                outBpsBd : outBpsBd.multiply(new BigDecimal(1024)).setScale(0, RoundingMode.DOWN));
        hostData.setPacketRate(BigDecimal.ZERO);
        hostData.setIsMaintain(0);

        return hostData;
    }

    private String stateConvert(String status) {
        if (status.equals("CONNECTED")) {
            return "Connected";
        }
        return "Disconnected";
    }
    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_HOST.code();
    }
}
